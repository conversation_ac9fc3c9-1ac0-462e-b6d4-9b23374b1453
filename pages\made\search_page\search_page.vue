<template>
	<view class="loading">
		<z-paging ref="paging" v-model="dataList" @query="queryList"
			bgColor="url('https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/userinfo_bg.png') no-repeat center top/100% 320rpx #fff "
			:refresher-status.sync="refresherStatus">
			<view slot="top">
				<cl-navbar title="" mpWeiXinShow :autoBack="true" bgColor="transparent" :fixed="false"
					class="custom_navbar">
					<view class="ordersearch" slot="center">
						<u-search placeholder="请输入" bgColor="#fff" :showAction="false" v-model="title"
							@search=""></u-search>
					</view>
				</cl-navbar>
			</view>
			<custom-refresher slot="refresher" :status="refresherStatus"></custom-refresher>
			<view class="loading_list">
				<view class="search_top">
					<view class="search_top_header">
						<view>龙湖兰园天序</view>
						<view>
							查看小区详情
							<u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
						</view>
					</view>
					<view class="search_top_main">
						<view class="search_top_main_left">
							<image :src="$t.getImgUrl()" mode="scaleToFill" />
						</view>
						<view class="search_top_main_right">
							<view>
								<view>住宅</view>
								|
								<view>仓山区</view>
								-
								<view>金山公园</view>
							</view>
							<view>
								<image src="" mode="scaleToFill" />
								推荐理由:
								<view>比同小区同户型均价便宜30%</view>
							</view>
							<view>
								22098<text>元/平</text>
							</view>
						</view>
					</view>
				</view>
				<view class="loading_container">
					<!-- 推荐标签区域 -->
					<view class="recommend_tags">
						<u-tabs :list="tabOption" lineWidth="32rpx" lineHeight="9rpx"
							:lineColor="`url(${lineBg}) 100% 100%`" :activeStyle="activeStyle"
							:inactiveStyle="inactiveStyle" itemStyle="height: 34px;"></u-tabs>

					</view>

					<!-- 筛选按钮区域 -->
					<view class="filter_buttons">
						<view class="filter_button">
							<text class="filter_text">售价</text>
							<u-icon name="arrow-down" size="24rpx" color="#333333"></u-icon>
						</view>
						<view class="filter_button">
							<text class="filter_text">户型</text>
							<u-icon name="arrow-down" size="24rpx" color="#333333"></u-icon>
						</view>
						<view class="filter_button">
							<text class="filter_text">面积</text>
							<u-icon name="arrow-down" size="24rpx" color="#333333"></u-icon>
						</view>
						<view class="filter_button">
							<text class="filter_text">排序</text>
							<u-icon name="arrow-down" size="24rpx" color="#333333"></u-icon>
						</view>
					</view>

					<!-- 筛选标签区域 -->
					<view class="filter_tags">
						<view class="filter_tag" :class="{ active: tagCurrent == index }"
							v-for="(item, index) in filterTags" :key="index" @click="tagCurrent = index">
							{{ item }}
						</view>
					</view>

					<!-- 房源列表 -->
					<view class="house_list">
						<house-item v-for="(item, index) in list" :key="index" :info="item"></house-item>
					</view>
				</view>
			</view>
			<view slot="bottom">
			</view>
		</z-paging>
	</view>
</template>

<script>
import HouseItem from '@/components/common/house_item.vue';

export default {
	components: {
		HouseItem
	},
	data() {
		return {
			lineBg: "https://inexkh.oss-cn-shenzhen.aliyuncs.com/d123/made/tabline.png",
			activeStyle: {
				'font-weight': 'bold',
				'font-size': '32rpx',
				'color': '#000000',
			},
			inactiveStyle: {
				'font-weight': 'bold',
				'font-size': '30rpx',
				'color': ' #777777',
			},
			refresherStatus: 0,
			title: "",
			filterTags: ['全部', '近地铁', '有学区', '全明格局', '大客厅', '有车位'],
			tagCurrent: 0,
			dataList: [],
			list: [
				{
					id: '1',
					title: '人气好房·龙湖天序标准三房 靠东门业主精装自住',
					rooms: '3室2厅',
					area: '90',
					orientation: '朝南',
					district: '龙湖兰园天序',
					price: 169,
					pricePerSqm: 18778,
					tags: ['近地铁', '有学区', '全明格局']
				},
				{
					id: '2',
					title: '次新社区 龙湖天序 96平 复式楼 145万 有证满',
					rooms: '3室2厅',
					area: '90',
					orientation: '朝南',
					district: '龙湖兰园天序',
					price: 169,
					pricePerSqm: 18778,
					tags: ['大客厅', '有车位', '有学区']
				},
				{
					id: '3',
					title: '龙湖天旭 近地铁 店长推荐 交通便利 满二 装修好 双卫',
					rooms: '3室2厅',
					area: '90',
					orientation: '朝南',
					district: '龙湖兰园天序',
					price: 169,
					pricePerSqm: 18778,
					tags: ['近地铁', '有学区', '全明格局']
				}
			],
		};
	},
	computed: {
		tabOption() {
			let refresherStatus = this.refresherStatus
			return [
				{ name: '推荐', val: '', disabled: refresherStatus === 0 ? false : true },
				{ name: '优质', val: '优质', disabled: refresherStatus === 0 ? false : true },
				{ name: '急售', val: '急售', disabled: refresherStatus === 0 ? false : true },


			]
		}
	},
	onLoad() { },
	methods: {
		queryList(pageNo, pageSize) {
			let params = {
				page: pageNo,
				page_size: pageSize,
			};
			// this.$api.getNews.getNewsList(params).then((res) => {
			// 	if (res.code == 200) {
			// 		this.$refs.paging.complete(res.result.data);
			// 		// this.$refs.paging.completeByNoMore(res.result, true); //:refresher-enabled="false" :show-loading-more-no-more-view="false" 
			// 	} else {
			// 		this.$refs.paging.complete(false);
			// 	}
			// });

		},
	},
};
</script>

<style lang="scss" scoped>
.loading {
	width: 100%;

	.ordersearch {
		width: 100%;
		padding: 0 20rpx 0 80rpx;
		border-radius: 70rpx;

		::v-deep {}
	}

	.loading_list {
		width: 100%;

		.search_top {
			padding: 20rpx 25rpx;

			.search_top_header {
				@include flex-center(row, space-between, center);

				>view:nth-child(1) {
					flex: 1;
					@include text_overflow(100%, 1);
					font-weight: bold;
					font-size: 36rpx;
					color: #000000;
				}

				>view:nth-child(2) {
					font-weight: 500;
					font-size: 26rpx;
					color: #777777;
					@include flex-center(row, center, center);
					gap: 5rpx;
				}
			}

			.search_top_main {
				@include flex-center(row, space-between, center);
				gap: 25rpx;
				margin-top: 25rpx;

				.search_top_main_left {
					width: 174rpx;
					height: 174rpx;
					border-radius: 12rpx;
					overflow: hidden;

					>image {
						width: 100%;
						height: 100%;
					}
				}
				.search_top_main_right{
					
				}
			}
		}

		.loading_container {
			padding: 25rpx;
			background: #FFFFFF;
			border-radius: 30rpx 30rpx 0rpx 0rpx;
		}

		// 推荐标签区域
		.recommend_tags {
			@include flex-center(row, flex-start, center);
			gap: 35rpx;

			.recommend_tag {
				font-weight: bold;
				font-size: 32rpx;
				color: #777777;

				&.active {
					color: #000000;
					position: relative;

					&::after {
						content: '';
						position: absolute;
						bottom: -10rpx;
						left: 50%;
						transform: translateX(-50%);
						width: 32rpx;
						height: 8rpx;
						background: #006AFC;
						border-radius: 4rpx;
					}
				}
			}
		}

		// 筛选按钮区域
		.filter_buttons {
			@include flex-center(row, space-between, center);
			margin-top: 40rpx;

			.filter_button {
				@include flex-center(row, center, center);
				gap: 8rpx;

				.filter_text {
					font-weight: 500;
					font-size: 26rpx;
					color: #333333;
				}
			}
		}

		// 筛选标签区域
		.filter_tags {
			@include flex-center(row, flex-start, center);
			margin-top: 30rpx;
			gap: 15rpx;
			overflow-x: auto;
			scroll-behavior: smooth;

			.filter_tag {
				flex-shrink: 0;
				padding: 8rpx 25rpx;
				border-radius: 100rpx;
				background: #F8F8F8;
				font-weight: 500;
				font-size: 24rpx;
				color: #707070;

				&.active {
					background: #006AFC;
					color: #FFFFFF;
				}
			}
		}

		// 房源列表
		.house_list {
			margin-top: 30rpx;

			// 重置house-item的外边距，因为我们在这里控制间距
			/deep/.house_item {
				margin-bottom: 0;
				padding: 25rpx 0;
				border-bottom: 1rpx solid #eee;
				border-radius: 0;

				&:last-child {
					border-bottom: 0;
				}
			}
		}
	}
}
</style>